#!/usr/bin/env python3
"""
Test script for Saan<PERSON> logic without Telegram dependencies
Use this to test the AI and memory functionality independently
"""

import asyncio
import json
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_memory_functions():
    """Test memory loading, saving, and updating"""
    print("🧠 Testing memory functions...")

    try:
        from core_functions import load_memory, save_memory, update_memory, build_context
        
        # Test basic memory operations
        test_user_id = 12345
        test_message = "Hello Saanvi!"
        test_reply = "अरे यार! कसा आहेस? 😘"
        
        # Update memory
        update_memory(test_user_id, test_message, test_reply)
        print("✅ Memory update works")
        
        # Load memory
        memory = load_memory()
        print("✅ Memory loading works")
        
        # Build context
        context, is_late = build_context(test_user_id)
        print("✅ Context building works")
        print(f"   Context length: {len(context)} chars")
        print(f"   Is late reply: {is_late}")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return False

def test_emotional_context():
    """Test emotional context detection"""
    print("\n💝 Testing emotional context...")

    try:
        from core_functions import get_emotional_context
        
        # Test different message types
        test_cases = [
            ("I love you baby", "sweet message"),
            ("Sorry, was in a meeting", "busy message"),
            ("Hey what's up", "normal message")
        ]
        
        for message, description in test_cases:
            context = get_emotional_context(False, message)
            print(f"✅ {description}: '{context[:50]}...' " if context else f"✅ {description}: no special context")
        
        return True
        
    except Exception as e:
        print(f"❌ Emotional context test failed: {e}")
        return False

def test_message_splitting():
    """Test message splitting functionality"""
    print("\n✂️ Testing message splitting...")

    try:
        from core_functions import split_message
        
        # Test cases
        test_messages = [
            "Short message",
            "This is a longer message that should be split into multiple parts because it exceeds the maximum length limit that we set for realistic conversation flow.",
            "अरे यार! तू कुठे होतास इतका वेळ? मला वाटत होतं तू मला विसरलास! 😢 पण आता तू आलास ना, मी खूप खुश आहे! 💕"
        ]
        
        for msg in test_messages:
            parts = split_message(msg, max_length=50)
            print(f"✅ Message split into {len(parts)} parts")
            for i, part in enumerate(parts, 1):
                print(f"   Part {i}: {part[:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Message splitting test failed: {e}")
        return False

async def test_openrouter_api():
    """Test OpenRouter API functionality"""
    print("\n🤖 Testing OpenRouter API...")

    try:
        from core_functions import call_openrouter_api
        from config import OPENROUTER_API_KEY
        
        if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == 'your_openrouter_api_key_here':
            print("⚠️  No OpenRouter API key configured - testing fallback mode")
        else:
            print("✅ OpenRouter API key found")
        
        # Test API call
        test_user_id = 12345
        test_message = "Hello Saanvi, how are you?"
        
        print("   Making API call...")
        response = await call_openrouter_api(test_user_id, test_message)
        
        print(f"✅ API call successful")
        print(f"   Response: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenRouter API test failed: {e}")
        return False

async def test_typing_delay():
    """Test typing delay calculation"""
    print("\n⏱️ Testing typing delay...")

    try:
        from core_functions import calculate_typing_delay
        
        test_messages = [
            "Hi",
            "This is a medium length message",
            "This is a very long message that should take more time to type and therefore should have a longer delay calculated for it"
        ]
        
        for msg in test_messages:
            delay = await calculate_typing_delay(msg)
            print(f"✅ Message ({len(msg)} chars) -> {delay:.2f}s delay")
        
        return True
        
    except Exception as e:
        print(f"❌ Typing delay test failed: {e}")
        return False

def test_config():
    """Test configuration loading"""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config import TELEGRAM_BOT_TOKEN, OPENROUTER_API_KEY
        
        if TELEGRAM_BOT_TOKEN and TELEGRAM_BOT_TOKEN != 'your_telegram_bot_token_here':
            print("✅ Telegram bot token configured")
        else:
            print("❌ Telegram bot token not configured")
            return False
        
        if OPENROUTER_API_KEY and OPENROUTER_API_KEY != 'your_openrouter_api_key_here':
            print("✅ OpenRouter API key configured")
        else:
            print("⚠️  OpenRouter API key not configured (fallback mode)")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🌟 Saanvi Bot Logic Test Suite")
    print("=" * 40)
    
    tests = [
        ("Configuration", test_config),
        ("Memory Functions", test_memory_functions),
        ("Emotional Context", test_emotional_context),
        ("Message Splitting", test_message_splitting),
        ("Typing Delay", test_typing_delay),
        ("OpenRouter API", test_openrouter_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
    
    print("\n" + "=" * 40)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Bot logic is working correctly.")
        print("   You can now try running the full bot with: python main.py")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("   See TROUBLESHOOTING.md for help with common issues.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
