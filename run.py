#!/usr/bin/env python3
"""
Simple runner script for Sa<PERSON><PERSON>
Checks dependencies and configuration before starting
"""

import sys
import os
import subprocess

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['telegram', 'httpx']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install dependencies with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def check_config():
    """Check if configuration is properly set"""
    try:
        from config import TELEGRAM_BOT_TOKEN, OPENROUTER_API_KEY
        
        if not TELEGRAM_BOT_TOKEN or TELEGRAM_BOT_TOKEN == 'your_telegram_bot_token_here':
            print("❌ Telegram Bot Token not configured!")
            print("   Edit config.py and add your bot token from @BotFather")
            return False
        
        if not OPENROUTER_API_KEY:
            print("⚠️  OpenRouter API Key not configured")
            print("   Bo<PERSON> will work with fallback responses only")
            print("   For better responses, get API key from openrouter.ai")
        else:
            print("✅ OpenRouter API Key configured")
        
        return True
        
    except ImportError:
        print("❌ config.py file not found!")
        print("   Create config.py with your tokens")
        return False

def main():
    """Main runner function"""
    print("🌟 Saanvi Bot Launcher")
    print("=" * 30)
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ Dependencies OK")
    
    # Check configuration
    print("\n🔧 Checking configuration...")
    if not check_config():
        sys.exit(1)
    print("✅ Configuration OK")
    
    # Start the bot
    print("\n🚀 Starting Saanvi Bot...")
    print("   Press Ctrl+C to stop")
    print("-" * 30)
    
    try:
        from main import *
        # The main.py will handle the actual bot execution
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
