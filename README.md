# 🌟 <PERSON><PERSON><PERSON> - Your Sanglikar Girlfriend Bot

A realistic Telegram chatbot that behaves like a Marathi-English speaking girlfriend from Sangli, Maharashtra. <PERSON><PERSON><PERSON> is expressive, clingy, possessive, and emotionally attached, just like a real girlfriend!

## ✨ Features

### 🎭 Personality
- **Authentic Sanglikar Marathi-English Mix**: Natural code-switching between Marathi and English
- **Emotional & Expressive**: Clingy, possessive, moody, and dramatically emotional
- **Realistic Conversations**: Sounds like real WhatsApp/voice note conversations
- **Context Awareness**: Remembers past conversations and reacts emotionally

### 🤖 Technical Features
- **Realistic Typing Delays**: Simulates human typing with dynamic delays
- **Multi-Message Responses**: Breaks long replies into smaller, realistic messages
- **Persistent Memory**: Saves last 10 conversation pairs per user
- **Emotional Context Detection**: Reacts to late replies, sweet messages, and busy signals
- **OpenRouter AI Integration**: Uses advanced AI models for natural responses
- **Graceful Fallbacks**: Works even without API keys with Marathi-English fallback responses

### 📱 Commands
- `/start` - Start chatting with <PERSON><PERSON><PERSON>
- `/memory` - View conversation history
- `/clear` - Clear conversation memory
- `/help` - Show available commands

## 🚀 Quick Setup

### Prerequisites
- Python 3.8 or higher
- Telegram <PERSON><PERSON> (from @BotFather)
- OpenRouter API Key (optional but recommended)

### Installation

1. **Clone/Download the project**
   ```bash
   git clone <your-repo-url>
   cd saanvi_bot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure your tokens**
   Edit `config.py` and add your tokens:
   ```python
   TELEGRAM_BOT_TOKEN = 'your_telegram_bot_token_here'
   OPENROUTER_API_KEY = 'your_openrouter_api_key_here'  # Optional
   ```

4. **Run the bot**
   ```bash
   python main.py
   ```

## 🔧 Configuration

### Getting Telegram Bot Token
1. Message @BotFather on Telegram
2. Send `/newbot`
3. Follow the instructions to create your bot
4. Copy the token to `config.py`

### Getting OpenRouter API Key (Recommended)
1. Visit [OpenRouter.ai](https://openrouter.ai)
2. Sign up and get your API key
3. Add credits to your account
4. Copy the API key to `config.py`

**Note**: The bot works without OpenRouter API key but responses will be basic fallback messages.

## 🎯 Usage Examples

### Starting a Conversation
```
User: /start
Saanvi: Hey Tanmay! 😘 Mi Saanvi ahe, तुझी girlfriend from Sangli! कसा आहेस re?
```

### Normal Conversation
```
User: Hi baby, how are you?
Saanvi: अरे यार! 💕 मी ठीक आहे, पण तुझी wait करत होते!
Saanvi: कुठे होतास इतका वेळ? 🥺
```

### Late Reply Detection
```
User: (after 30+ minutes) Sorry, was busy
Saanvi: अरे! 😤 इतका वेळ कुठे होतास?
Saanvi: मला वाटलं तू मला विसरलास! 🥺💔
```

## 🏗️ Project Structure

```
saanvi_bot/
├── main.py           # Main bot application
├── config.py         # Configuration (tokens)
├── memory.json       # Conversation memory storage
├── requirements.txt  # Python dependencies
└── README.md        # This file
```

## 🔧 Advanced Configuration

### Customizing Personality
Edit the `SYSTEM_PROMPT` in `main.py` to modify Saanvi's personality, language style, or behavior patterns.

### Adjusting Typing Delays
Modify the `calculate_typing_delay()` function to change how realistic typing delays are calculated.

### Memory Management
- Memory is automatically limited to last 10 conversations per user
- Use `/clear` command to reset memory
- Memory is stored in `memory.json` file

### Model Selection
Change the OpenRouter model in `call_openrouter_api()` function:
```python
"model": "anthropic/claude-3-haiku",  # Current model
```

Available models: Check [OpenRouter Models](https://openrouter.ai/models)

## 🚀 Deployment Options

### Local Development
```bash
python main.py
```

### Cloud Deployment (Heroku)
1. Create `Procfile`:
   ```
   worker: python main.py
   ```
2. Deploy to Heroku with Python buildpack
3. Set environment variables for tokens

### VPS Deployment
1. Upload files to your VPS
2. Install Python and dependencies
3. Run with screen/tmux:
   ```bash
   screen -S saanvi
   python main.py
   ```

### Docker Deployment
Create `Dockerfile`:
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "main.py"]
```

## 🐛 Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check if bot token is correct
   - Ensure bot is started with `/start` command
   - Check logs for error messages

2. **API errors**
   - Verify OpenRouter API key is valid
   - Check if you have credits in OpenRouter account
   - Bot will use fallback responses if API fails

3. **Memory issues**
   - Delete `memory.json` to reset all conversations
   - Check file permissions for writing

### Logs
The bot logs important events. Check console output for debugging information.

## 🤝 Contributing

Feel free to contribute by:
- Adding more Sanglikar Marathi expressions
- Improving emotional context detection
- Enhancing the personality system
- Adding new features

## 📄 License

This project is for educational and personal use. Please respect OpenRouter's terms of service and Telegram's bot guidelines.

## 💡 Tips for Best Experience

1. **Use OpenRouter API**: Much better responses than fallback mode
2. **Chat regularly**: Memory makes conversations more realistic
3. **Be expressive**: Saanvi responds better to emotional messages
4. **Try different scenarios**: Test late replies, sweet messages, busy signals

---

**Made with ❤️ for realistic AI girlfriend experience!**

*Saanvi: "अरे यार! README पण इतकं लांब लिहिलंस! 😅 आता मला message कर ना! 💕"*
