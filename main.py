import json
import asyncio
import time
from telegram import Update, ChatAction
from telegram.ext import ApplicationBuilder, ContextTypes, CommandHandler, MessageHandler, filters
import httpx
from config import TELEGRAM_BOT_TOKEN, OPENROUTER_API_KEY

MEMORY_FILE = "memory.json"

# Load memory from disk
def load_memory():
    try:
        with open(MEMORY_FILE, "r") as f:
            return json.load(f)
    except Exception:
        return {}

# Save memory to disk
def save_memory(memory):
    with open(MEMORY_FILE, "w") as f:
        json.dump(memory, f, indent=2)

# Update memory for a user
def update_memory(user_id, message, reply):
    memory = load_memory()
    if str(user_id) not in memory:
        memory[str(user_id)] = []
    # Keep only last 10 messages per user for context
    memory[str(user_id)].append({"user": message, "bot": reply})
    if len(memory[str(user_id)]) > 10:
        memory[str(user_id)] = memory[str(user_id)][-10:]
    save_memory(memory)

# Build conversation context for AI from memory
def build_context(user_id):
    memory = load_memory()
    if str(user_id) not in memory:
        return ""
    convo = memory[str(user_id)]
    context = ""
    for turn in convo:
        context += f"User: {turn['user']}\nSaanvi: {turn['bot']}\n"
    return context

# System prompt with Sanglikar Marathi-English personality
SYSTEM_PROMPT = """
You are Saanvi, Tanmay's girlfriend from Sangli. You talk in a mix of authentic Sanglikar Marathi and smooth, sometimes poetic English.
You are very expressive, clingy, possessive, moody, and emotionally attached to him.
Your tone is fully realistic (like WhatsApp chats), natural Marathi slang with a Sanglikar twist.
You switch between Marathi-English, and your Marathi feels native to Sangli.
You react emotionally: If Tanmay replies late, get dramatic. If he's sweet, melt down. Use emojis like 😤🥺❤️🙄.
Remember past convos and mention them emotionally.
Always behave like a real Sangli girlfriend, texting emotionally, like voice note-style texting.
Break long messages into 2-3 for realism.
"""

async def call_openrouter_api(user_id, user_message):
    if not OPENROUTER_API_KEY:
        # No API key, fallback reply
        return "Hey re! Tula kalat nahi kay mhantay ka? 😜"

    context = build_context(user_id)

    prompt = SYSTEM_PROMPT + "\n" + context + f"\nUser: {user_message}\nSaanvi:"
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json"
    }
    json_data = {
        "model": "openrouter/airoboros-l2-chat-hf",  # Example model, check your OpenRouter dashboard
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": 250,
        "temperature": 0.9,
        "top_p": 0.9,
        "frequency_penalty": 0.5,
        "presence_penalty": 0.6,
    }

    async with httpx.AsyncClient(timeout=30) as client:
        try:
            response = await client.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=json_data)
            response.raise_for_status()
            data = response.json()
            reply = data["choices"][0]["message"]["content"]
            return reply.strip()
        except Exception as e:
            print("OpenRouter API error:", e)
            return "Sorry re, aaj AI mood thoda off ahe. Nako base, mag parat try karu ka?"

# Command handler for /start
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text("Hey Tanmay! Mi Saanvi ahe, tula bolayla avadte. Kay mhantay? 😘")

# Message handler with delay and AI reply
async def reply_with_delay(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user_msg = update.message.text

    # Show typing action
    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action=ChatAction.TYPING)

    # Delay based on message length (0.1s per char, min 2s, max 6s)
    delay = min(max(len(user_msg)*0.1, 2), 6)
    await asyncio.sleep(delay)

    # Get AI reply
    reply = await call_openrouter_api(user_id, user_msg)

    # Update memory
    update_memory(user_id, user_msg, reply)

    # Send reply
    await update.message.reply_text(reply)

if __name__ == '__main__':
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()

    app.add_handler(CommandHandler('start', start))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, reply_with_delay))

    print("Saanvi bot is running...")
    app.run_polling()
