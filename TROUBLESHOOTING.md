# 🔧 Troubleshooting Guide for Saanvi Bot

## Common Installation Issues

### 1. Python Version Compatibility

**Problem**: `AttributeError: 'typing.Union' object has no attribute '__module__'`

**Solution**: This is usually a Python 3.11+ compatibility issue with httpcore/httpx.

**Try these solutions in order**:

```bash
# Option 1: Use specific compatible versions
pip uninstall python-telegram-bot httpx httpcore
pip install python-telegram-bot==20.3 httpx==0.24.1

# Option 2: Use older httpcore
pip install httpcore==0.17.3

# Option 3: Use Python 3.10 or earlier
# Download Python 3.10 from python.org

# Option 4: Use virtual environment with specific Python version
python3.10 -m venv saanvi_env
source saanvi_env/bin/activate  # On Windows: saanvi_env\Scripts\activate
pip install python-telegram-bot==20.3 httpx==0.24.1
```

### 2. Telegram Bot Token Issues

**Problem**: <PERSON><PERSON> doesn't respond or gives authentication errors

**Solutions**:
1. Check if token is correct in `config.py`
2. Make sure bot is started with `/start` command
3. Verify bot token with <PERSON><PERSON><PERSON><PERSON>:
   ```
   Message @BotFather on Telegram
   Send: /mybots
   Select your bot
   Check token
   ```

### 3. OpenRouter API Issues

**Problem**: API errors or no AI responses

**Solutions**:
1. Check API key in `config.py`
2. Verify you have credits in OpenRouter account
3. Test API key manually:
   ```bash
   curl -X POST "https://openrouter.ai/api/v1/chat/completions" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model": "anthropic/claude-3-haiku", "messages": [{"role": "user", "content": "test"}]}'
   ```

### 4. Memory/JSON Issues

**Problem**: Memory not saving or JSON errors

**Solutions**:
1. Delete `memory.json` file to reset
2. Check file permissions in bot directory
3. Ensure bot has write access to directory

### 5. Import Errors

**Problem**: `ModuleNotFoundError` for telegram or httpx

**Solutions**:
```bash
# Reinstall dependencies
pip uninstall python-telegram-bot httpx
pip install -r requirements.txt

# Or install manually
pip install python-telegram-bot httpx

# Check installation
python -c "import telegram; print('Telegram bot library OK')"
python -c "import httpx; print('HTTPX library OK')"
```

## Platform-Specific Issues

### Windows
- Use `python` instead of `python3`
- Use `Scripts\activate` instead of `bin/activate` for virtual environments
- Install Microsoft Visual C++ if you get compilation errors

### macOS
- Use `python3` and `pip3`
- Install Xcode command line tools: `xcode-select --install`

### Linux
- Install Python dev packages: `sudo apt-get install python3-dev`
- Use `python3` and `pip3`

## Testing Your Installation

### Step-by-Step Test

1. **Test Python basics**:
   ```bash
   python -c "import json, asyncio, logging; print('Basic Python OK')"
   ```

2. **Test dependencies**:
   ```bash
   python -c "import telegram; print('Telegram library OK')"
   python -c "import httpx; print('HTTPX library OK')"
   ```

3. **Test configuration**:
   ```bash
   python -c "from config import TELEGRAM_BOT_TOKEN; print('Config OK' if TELEGRAM_BOT_TOKEN else 'Token missing')"
   ```

4. **Test bot syntax**:
   ```bash
   python -c "import main; print('Bot code syntax OK')"
   ```

### Manual Testing

If automatic testing fails, test the bot manually:

1. Start the bot: `python main.py`
2. Message your bot on Telegram with `/start`
3. Send a test message
4. Check console for error messages

## Alternative Installation Methods

### Method 1: Conda Environment
```bash
conda create -n saanvi python=3.10
conda activate saanvi
pip install python-telegram-bot==20.3 httpx==0.24.1
```

### Method 2: Docker (if you have Docker)
```dockerfile
FROM python:3.10-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install python-telegram-bot==20.3 httpx==0.24.1
COPY . .
CMD ["python", "main.py"]
```

### Method 3: Pipenv
```bash
pip install pipenv
pipenv install python-telegram-bot==20.3 httpx==0.24.1
pipenv shell
python main.py
```

## Getting Help

If you're still having issues:

1. **Check the logs**: Look at console output for specific error messages
2. **Update dependencies**: Try newer/older versions of packages
3. **Python version**: Consider using Python 3.10 if you have 3.11+
4. **Virtual environment**: Always use a clean virtual environment
5. **Minimal test**: Try running just the basic imports first

## Working Configurations

These combinations are known to work:

- **Python 3.10** + `python-telegram-bot==20.3` + `httpx==0.24.1`
- **Python 3.9** + `python-telegram-bot==20.7` + `httpx==0.25.2`
- **Python 3.8** + `python-telegram-bot==20.3` + `httpx==0.24.1`

## Emergency Fallback

If nothing works, you can run a simplified version without the telegram library for testing the AI logic:

```python
# test_ai.py - Simple test without Telegram
import asyncio
from main import call_openrouter_api

async def test():
    response = await call_openrouter_api(12345, "Hello Saanvi!")
    print(f"AI Response: {response}")

asyncio.run(test())
```

This will help you isolate whether the issue is with Telegram integration or the AI logic.
