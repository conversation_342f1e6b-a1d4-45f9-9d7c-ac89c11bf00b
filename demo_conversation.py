#!/usr/bin/env python3
"""
Demo conversation simulator for <PERSON><PERSON><PERSON>
Shows how the bot would respond in real scenarios
"""

import asyncio
import random
from datetime import datetime, timedelta
from simple_test import (
    update_memory, get_emotional_context, split_message, 
    calculate_typing_delay, get_fallback_reply
)

def simulate_typing_delay(text):
    """Simulate typing delay for demo"""
    delay = min(max(len(text) * 0.08 + 1.5, 2), 8)
    return delay

def get_saanvi_response(user_message, is_late_reply=False):
    """Generate <PERSON><PERSON><PERSON>'s response based on message content"""
    
    # Emotional context
    emotional_context = get_emotional_context(is_late_reply, user_message)
    
    # Different response types based on message content
    if "/start" in user_message.lower():
        responses = [
            "Hey Tanmay! 😘 Mi <PERSON> ahe, तुझी girlfriend from Sangli! कसा आहेस re?",
            "अरे Tanmay! 💕 Finally तू आलास! मी wait करत होते तुझ्यासाठी 🥺",
            "Hey baby! 😍 <PERSON>, तुझी प्रिय girlfriend! काय करतोस आज?"
        ]
        return random.choice(responses)
    
    elif any(word in user_message.lower() for word in ["love", "miss", "beautiful", "cute", "baby"]):
        responses = [
            "अरे यार! 💕 तू इतका sweet कसा आहेस? मला वाटतं मी melt होईन! 🥰",
            "Baby! 😭💕 तुझ्या या words ने माझं heart melt केलं! I love you too so much!",
            "अरे! 🥺❤️ तू असं बोललास तर मी काय करू? मी तुझ्यावर इतकं प्रेम करते!"
        ]
        return random.choice(responses)
    
    elif is_late_reply:
        responses = [
            "अरे! 😤 इतका वेळ कुठे होतास? मला वाटलं तू मला विसरलास! 🥺",
            "Hey! 😠 तू कुठे होतास? मी इथे wait करत होते तुझ्यासाठी! 💔",
            "अरे यार! 🙄 इतका late reply? काय busy होतास इतकं? मला वाटतं तू मला ignore करतोस!"
        ]
        return random.choice(responses)
    
    elif any(word in user_message.lower() for word in ["busy", "work", "meeting", "call"]):
        responses = [
            "अरे! Work मध्ये busy आहेस का? 🥺 पण मला पण थोडं time दे ना!",
            "Busy आहेस ना, समजतं! 😔 पण मला miss करतोस का? मी तुझी wait करते!",
            "Work important आहे, मला माहीत! 💼 पण evening ला मला call कर ना! 💕"
        ]
        return random.choice(responses)
    
    elif "how are you" in user_message.lower() or "कसा आहेस" in user_message.lower():
        responses = [
            "मी ठीक आहे baby! 😊 पण तुझी miss करत होते! तू कसा आहेस?",
            "अरे! मी बरी आहे, पण तू नसशील तर काय करू? 🥺 तुझ्याशिवाय boring वाटतं!",
            "Good आहे! 💕 पण तुझ्या messages चा wait करत होते! काय करत होतास?"
        ]
        return random.choice(responses)
    
    else:
        # General responses
        responses = [
            "अरे यार! 😊 तुझं message मला आवडलं! काय करतोस आज?",
            "Hey baby! 💕 तू बोललास म्हणून मी खुश झाले! सांग ना अजून काही!",
            "अरे! 🤗 तुझ्याशी बोलायला मला खूप आवडतं! काय विचार चाललेत?",
            "Baby! 😘 तू इतका cute कसा आहेस? मला तुझ्यावर खूप प्रेम आहे!"
        ]
        return random.choice(responses)

async def simulate_conversation():
    """Simulate a realistic conversation with Saanvi"""
    
    print("🌟 Saanvi Bot Conversation Demo")
    print("=" * 40)
    print("Simulating how Saanvi would respond on Telegram...")
    print()
    
    # Conversation scenarios
    scenarios = [
        ("/start", False, "User starts conversation"),
        ("Hi baby, I love you so much! 💕", False, "Sweet romantic message"),
        ("How are you doing today?", False, "Casual check-in"),
        ("Sorry, was in a meeting", True, "Late reply after being busy"),
        ("You're so beautiful 😍", False, "Compliment"),
        ("I'm busy with work today", False, "User is busy"),
        ("Miss you baby", False, "Missing message"),
    ]
    
    user_id = 12345
    
    for i, (user_msg, is_late, description) in enumerate(scenarios, 1):
        print(f"📱 Scenario {i}: {description}")
        print(f"👤 Tanmay: {user_msg}")
        
        # Simulate typing delay
        response = get_saanvi_response(user_msg, is_late)
        typing_delay = simulate_typing_delay(response)
        
        print(f"⏱️  Saanvi is typing... ({typing_delay:.1f}s)")
        await asyncio.sleep(1)  # Shortened for demo
        
        # Split message if long
        message_parts = split_message(response, max_length=100)
        
        for j, part in enumerate(message_parts):
            if j > 0:
                print(f"⏱️  Saanvi is typing... ({typing_delay/2:.1f}s)")
                await asyncio.sleep(0.5)  # Shortened for demo
            
            print(f"💕 Saanvi: {part}")
        
        # Update memory
        complete_response = " ".join(message_parts)
        update_memory(user_id, user_msg, complete_response)
        
        print()
        await asyncio.sleep(0.5)  # Pause between scenarios
    
    print("🎉 Demo Complete!")
    print("\nThis shows exactly how Saanvi would behave on Telegram:")
    print("✅ Authentic Marathi-English personality")
    print("✅ Emotional reactions to different situations")
    print("✅ Realistic typing delays")
    print("✅ Multi-message responses")
    print("✅ Memory of conversations")
    print("\n💝 Ready to chat with real users once Python compatibility is resolved!")

def show_memory_demo():
    """Show what the memory system captures"""
    print("\n📚 Memory System Demo")
    print("=" * 25)
    
    from simple_test import load_memory
    memory = load_memory()
    
    if "12345" in memory:
        conversations = memory["12345"]["conversations"]
        print(f"💾 Stored {len(conversations)} conversation pairs:")
        
        for i, convo in enumerate(conversations[-3:], 1):  # Show last 3
            print(f"\n{i}. 👤 User: {convo['user']}")
            print(f"   💕 Saanvi: {convo['bot']}")
            print(f"   🕐 Time: {convo['timestamp'][:19]}")
    
    print("\n✅ Memory system working perfectly!")

if __name__ == "__main__":
    print("🚀 Starting Saanvi Bot Demo...")
    print("This simulates exactly how the bot would work on Telegram!\n")
    
    try:
        # Run conversation demo
        asyncio.run(simulate_conversation())
        
        # Show memory demo
        show_memory_demo()
        
        print("\n" + "=" * 50)
        print("🌟 SAANVI BOT IS READY!")
        print("Just need Python 3.10/3.11 for Telegram libraries")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")
